"""
比特浏览器自动化币安买卖脚本主程序

这是整个项目的主入口文件，负责：
1. 创建和管理PyQt6图形界面
2. 连接界面控件与后端功能
3. 处理用户交互事件
4. 管理浏览器列表显示和状态更新
5. 协调各个功能模块的工作

主要类：
- MainWindow: 主窗口类，继承自QDialog，管理整个应用程序的界面和逻辑
"""

# 导入系统模块
import sys  # 系统相关功能，用于程序退出等
import time  # 时间相关功能，用于延迟等待

# 导入PyQt6界面相关模块
from PyQt6.QtWidgets import (
    QApplication,    # 应用程序主类
    QDialog,         # 对话框基类
    QTreeWidgetItem, # 树形控件项
    QPushButton,     # 按钮控件
    QMessageBox,     # 消息框
    QLineEdit,       # 单行文本输入框
    QWidget,         # 基础控件类
    QHBoxLayout      # 水平布局管理器
)
from PyQt6.QtCore import Qt, pyqtSlot, QTimer  # Qt核心功能：常量、槽装饰器、定时器

# 导入项目自定义模块
from ui_main import Ui_Dialog  # 界面设计文件（由Qt Designer生成）
from browser_automation import BrowserAutomation, show_error_message, show_info_message  # 浏览器自动化核心功能
from config import get_config  # 配置管理模块
from PyQt6.QtWidgets import QStyledItemDelegate


class ReadOnlyDelegate(QStyledItemDelegate):
    def __init__(self, editable_columns, parent=None):
        super().__init__(parent)
        self.editable_columns = editable_columns

    def createEditor(self, parent, option, index):
        if index.column() in self.editable_columns:
            return super().createEditor(parent, option, index)
        return None  # 不可编辑

class MainWindow(QDialog):
    """
    主窗口类 - 应用程序的核心界面类

    继承自QDialog，负责管理整个应用程序的图形界面和用户交互。

    主要职责：
    1. 初始化和设置图形界面
    2. 连接界面控件与后端功能
    3. 处理用户的各种操作事件
    4. 管理浏览器列表的显示和更新
    5. 协调自动化任务的执行

    属性：
    - ui: 界面对象，包含所有界面控件
    - browser_automation: 浏览器自动化功能对象
    - browser_data: 存储浏览器原始数据的字典
    - status_timer: 定时器，用于定期刷新状态
    """

    def __init__(self):
        """
        构造函数 - 初始化主窗口

        执行顺序：
        1. 调用父类构造函数
        2. 设置界面
        3. 初始化浏览器自动化功能
        4. 连接信号和槽
        5. 加载数据和设置默认值
        6. 启动定时器
        """
        # 调用父类QDialog的构造函数
        super().__init__()

        # 创建界面对象并设置到当前窗口
        self.ui = Ui_Dialog()  # 创建界面对象（来自ui_main.py）
        self.ui.setupUi(self)  # 将界面设置到当前窗口

        # 初始化浏览器自动化核心功能
        self.browser_automation = BrowserAutomation()  # 创建浏览器自动化对象

        # 连接信号和槽 - 用于线程间通信
        # 当浏览器状态更新时，调用update_browser_status方法更新界面
        self.browser_automation.status_updated.connect(self.update_browser_status)
        # 当运行次数更新时，调用update_run_count方法更新界面
        self.browser_automation.run_count_updated.connect(self.update_run_count)

        # 存储浏览器原始数据的字典 {browser_id: browser_info}
        self.browser_data = {}

        # 绑定所有按钮的点击事件
        self.setup_connections()

        # 从比特浏览器API加载浏览器列表并显示在界面上
        self.load_browser_list()

        # 设置树形控件的属性（列宽、编辑功能等）
        self.setup_tree_widget()

        # 设置定时器，用于定期刷新浏览器状态
        ui_config = get_config('ui')  # 获取界面配置
        self.status_timer = QTimer()  # 创建定时器对象
        self.status_timer.timeout.connect(self.refresh_status)  # 连接超时信号到刷新方法
        self.status_timer.start(ui_config['status_refresh_interval'])  # 启动定时器（默认5秒间隔）

        # 设置窗口标题
        self.setWindowTitle(ui_config['window_title'])

        # 从配置文件设置默认的延迟值
        trade_config = get_config('default_trade')  # 获取交易配置
        self.ui.lineEdit.setText(trade_config['min_delay'])      # 设置最小延迟输入框
        self.ui.lowlatency.setText(trade_config['max_delay'])    # 设置最大延迟输入框

        # 设置默认最大浏览器数量（如果界面有相关控件）
        security_config = get_config('security')  # 获取安全配置
        if hasattr(self.ui, 'max_browsers_input'):  # 检查界面是否有最大浏览器数量输入框
            self.ui.max_browsers_input.setText(str(security_config['default_max_browsers']))

        # 更新浏览器数量显示（在窗口标题中显示当前数量）
        self.update_browser_count_display()

    def update_browser_count_display(self):
        """更新浏览器数量显示"""
        count_info = self.browser_automation.get_browser_count_info()

        # 更新窗口标题，显示当前浏览器数量
        base_title = get_config('ui')['window_title']
        title_with_count = f"{base_title} - 浏览器: {count_info['status_text']}"
        self.setWindowTitle(title_with_count)

        # 如果有专门的数量显示标签，也可以更新
        if hasattr(self.ui, 'browser_count_label'):
            self.ui.browser_count_label.setText(f"运行中: {count_info['status_text']}")

    def set_max_browsers_from_input(self):
        """从界面输入框设置最大浏览器数量"""
        if hasattr(self.ui, 'max_browsers_input'):
            try:
                max_count = int(self.ui.max_browsers_input.text())
                if max_count > 0:
                    self.browser_automation.set_max_browsers(max_count)
                    self.update_browser_count_display()
                    show_info_message("设置成功", f"最大浏览器数量已设置为: {max_count}")
                else:
                    show_error_message("设置错误", "最大浏览器数量必须大于0")
            except ValueError:
                show_error_message("设置错误", "请输入有效的数字")



    def setup_connections(self):
        """
        设置按钮连接 - 将界面按钮的点击事件连接到对应的处理方法

        这个方法建立了界面控件与后端功能之间的桥梁。
        每个按钮的clicked信号都连接到一个特定的槽函数。

        连接的按钮和对应功能：
        - Launchbrowserall: 启动全部浏览器
        - Closeall: 关闭全部浏览器
        - Startall: 开始全部自动化任务
        - Stopall: 停止全部自动化任务
        - Launchbrowser: 启动选中的浏览器
        - Start: 开始选中的自动化任务
        - Pause: 暂停选中的自动化任务
        - stop: 停止选中的自动化任务
        """
        # 批量操作按钮连接
        self.ui.Launchbrowserall.clicked.connect(self.launch_all_browsers)  # 启动全部浏览器按钮
        self.ui.Closeall.clicked.connect(self.close_all_browsers)            # 关闭全部浏览器按钮
        self.ui.Startall.clicked.connect(self.start_all_automation)          # 开始全部自动化按钮
        self.ui.Stopall.clicked.connect(self.stop_all_automation)            # 停止全部自动化按钮

        # 单个操作按钮连接
        self.ui.Launchbrowser.clicked.connect(self.launch_selected_browser)  # 启动选中浏览器按钮
        self.ui.Start.clicked.connect(self.start_selected_automation)        # 开始选中自动化按钮
        self.ui.Pause.clicked.connect(self.pause_selected_automation)        # 暂停选中自动化按钮
        self.ui.stop.clicked.connect(self.stop_selected_automation)          # 停止选中自动化按钮

    def setup_tree_widget(self):
        """
        设置TreeWidget（树形控件）的属性和行为

        TreeWidget是显示浏览器列表的主要控件，类似于Excel表格。
        这个方法负责：
        1. 设置各列的宽度
        2. 连接双击编辑事件
        3. 配置控件的显示属性

        列结构说明：
        0: 名称 - 显示浏览器窗口名称（只读）
        1: 买入价格 - 可编辑，设置交易价格
        2: 买入数量 - 可编辑，设置交易数量
        3: 状态 - 显示浏览器和自动化状态（只读）
        4: 运行次数 - 显示已执行的交易次数（只读）
        5: 设置运行次数 - 可编辑，设置目标执行次数
        6: 开关 - 包含启动和开始按钮（只读）
        """
        # 从配置文件获取列宽设置，实现配置化管理
        ui_config = get_config('ui')  # 获取界面配置字典
        column_widths = ui_config['column_widths']  # 获取列宽配置字典

        # 设置各列的宽度（像素值）
        # setColumnWidth(列索引, 宽度) - 设置指定列的宽度
        self.ui.treeWidget.setColumnWidth(0, column_widths['name'])           # 名称列宽度
        self.ui.treeWidget.setColumnWidth(1, column_widths['buy_price'])      # 买入价格列宽度
        self.ui.treeWidget.setColumnWidth(2, column_widths['buy_quantity'])   # 买入数量列宽度
        self.ui.treeWidget.setColumnWidth(3, column_widths['status'])         # 状态列宽度
        self.ui.treeWidget.setColumnWidth(4, column_widths['run_count'])      # 运行次数列宽度
        self.ui.treeWidget.setColumnWidth(5, column_widths['set_run_count'])  # 设置运行次数列宽度
        self.ui.treeWidget.setColumnWidth(6, column_widths['actions'])        # 开关列宽度

        # 连接双击编辑事件 - 当用户双击某个单元格时触发编辑功能
        # 注释掉是因为可能在测试中被禁用，可以根据需要启用
        #self.ui.treeWidget.itemDoubleClicked.connect(self.on_item_double_clicked)

    def load_browser_list(self):
        """
        加载浏览器列表 - 从比特浏览器API获取数据并构建界面显示

        这是程序启动时的关键方法，负责构建整个浏览器列表界面。

        工作流程：
        1. 调用API获取未启动的浏览器列表
        2. 清空现有界面内容
        3. 为每个浏览器创建一行显示
        4. 设置默认的交易参数
        5. 配置可编辑属性
        6. 创建操作按钮
        7. 建立数据关联

        数据流向：
        API响应 → 解析数据 → 创建界面项 → 设置显示内容 → 添加到界面
        """
        # ========== API数据获取阶段 ==========
        # 调用浏览器自动化模块获取浏览器列表
        # page=0 表示获取未启动的浏览器窗口（status=0的浏览器）
        result = self.browser_automation.get_browser_list(page=0)

        # ========== 数据处理阶段 ==========
        if result.get("success"):  # 检查API调用是否成功
            # 从API响应中提取浏览器列表数据
            # API返回格式：{"success": True, "data": {"list": [浏览器数组]}}
            browsers = result["data"]["list"]

            # 清空树形控件中的现有内容，准备重新加载
            # 这确保每次刷新时都是最新的数据
            self.ui.treeWidget.clear()

            # ========== 界面构建阶段 ==========
            # 遍历每个浏览器，为其创建一行显示
            for browser in browsers:
                # 获取浏览器的唯一标识符（用于后续所有操作的关键）
                browser_id = browser['id']

                # 将浏览器原始数据存储到本地字典中
                # 这样可以在后续操作中快速查找浏览器的详细信息
                # 例如：浏览器名称、备注、配置等
                self.browser_data[browser_id] = browser

                # 从配置文件获取默认的交易参数
                # 这实现了配置化管理，便于统一修改默认值
                trade_config = get_config('default_trade')

                # ========== 创建界面行 ==========
                # 创建一个新的树形项（相当于Excel表格的一行）
                item = QTreeWidgetItem()

                # 设置各列的显示内容（setText(列索引, 显示文本)）
                item.setText(0, browser['name'])                    # 第0列：浏览器名称（来自API）
                item.setText(1, trade_config['buy_price'])          # 第1列：默认买入价格（来自配置）
                item.setText(2, trade_config['buy_quantity'])       # 第2列：默认买入数量（来自配置）
                item.setText(3, "未启动")                           # 第3列：初始状态（固定值）
                item.setText(4, "0")                               # 第4列：运行次数（初始为0）
                item.setText(5, trade_config['run_count'])          # 第5列：设置运行次数（来自配置）

                # ========== 界面样式设置 ==========
                # 设置所有列的文本居中对齐，提升视觉效果
                for col in range(item.columnCount()):
                    item.setTextAlignment(col, Qt.AlignmentFlag.AlignCenter)

                # 设置特定列为可编辑状态
                # 列1(买入价格)、列2(买入数量)、列5(设置运行次数) 可以双击编辑
                for col in [1, 2, 5]:
                    # 添加ItemIsEditable标志，使该列可编辑
                    item.setFlags(item.flags() | Qt.ItemFlag.ItemIsEditable)

                # ========== 数据关联 ==========
                # 将浏览器ID存储到界面项的用户数据中
                # UserRole是Qt提供的用户自定义数据存储角色
                # 这建立了界面显示与业务数据之间的关联桥梁
                item.setData(0, Qt.ItemDataRole.UserRole, browser_id)

                # 将创建的行添加到树形控件中显示
                self.ui.treeWidget.addTopLevelItem(item)

                # 创建开关列的按钮
                self.create_action_buttons(item, browser_id)
            delegate = ReadOnlyDelegate(editable_columns=[1, 2, 5])
            self.ui.treeWidget.setItemDelegate(delegate)
            from PyQt6.QtWidgets import QAbstractItemView
            self.ui.treeWidget.setEditTriggers(QAbstractItemView.EditTrigger.DoubleClicked)
        else:
            show_error_message("错误", f"获取浏览器列表失败: {result.get('message', '未知错误')}")

    def create_action_buttons(self, item, browser_id):
        """为每行创建启动和开始按钮"""
        button_widget = QWidget()
        layout = QHBoxLayout(button_widget)
        layout.setContentsMargins(2, 2, 2, 2)

        # 启动按钮
        launch_btn = QPushButton("启动")
        launch_btn.setFixedSize(50, 25)
        launch_btn.clicked.connect(lambda: self.launch_browser(browser_id))

        # 开始/暂停按钮
        start_btn = QPushButton("开始")
        start_btn.setFixedSize(50, 25)
        start_btn.clicked.connect(lambda: self.toggle_automation(browser_id, start_btn))

        layout.addWidget(launch_btn)
        layout.addWidget(start_btn)

        # 设置到树形控件
        self.ui.treeWidget.setItemWidget(item, 6, button_widget)

        # 存储按钮引用
        item.setData(6, Qt.ItemDataRole.UserRole, {'launch': launch_btn, 'start': start_btn})

    def refresh_status(self):
        """
        刷新所有浏览器状态 - 定时更新界面上所有浏览器的状态显示

        这个函数的作用：
        定期检查所有浏览器的最新状态，并更新界面上的显示内容，
        包括状态文本和按钮的文字、功能。

        调用时机：
        - 由定时器每5秒自动调用一次
        - 确保界面显示与实际状态保持同步

        工作流程：
        1. 遍历界面表格中的每一行
        2. 获取每行对应的浏览器ID
        3. 查询该浏览器的最新状态
        4. 更新状态文本显示
        5. 根据状态更新按钮的文字和功能

        为什么需要定时刷新？
        - 浏览器状态可能在程序外部发生变化
        - 确保按钮功能与实际状态匹配
        - 提供实时的状态反馈给用户
        """
        # ========== 第一步：遍历界面中的每一行 ==========
        # topLevelItemCount() 返回表格中总共有多少行
        # range() 生成从0开始的数字序列：0, 1, 2, 3, ...
        # 这个循环会依次处理表格中的每一行
        for i in range(self.ui.treeWidget.topLevelItemCount()):
            # topLevelItem(i) 获取第i行的数据对象
            # item 代表界面表格中的一整行，包含所有列的数据
            item = self.ui.treeWidget.topLevelItem(i)

            # 从第0列的用户数据中获取浏览器ID
            # 这个ID是之前用setData()存储的，现在用data()取出来
            # Qt.ItemDataRole.UserRole 是用户自定义数据的存储位置
            browser_id = item.data(0, Qt.ItemDataRole.UserRole)

            # ========== 第二步：更新状态文本 ==========
            # 调用业务逻辑模块，获取这个浏览器的最新状态
            # get_browser_status() 会返回中文状态描述，如"已启动"、"自动化运行中"等
            status = self.browser_automation.get_browser_status(browser_id)

            # setText(列索引, 新文本) 更新第3列（状态列）的显示文本
            item.setText(3, status)

            # ========== 第三步：更新按钮状态 ==========
            # 从第6列的用户数据中获取按钮对象的引用
            # 这些按钮是之前用create_action_buttons()创建并存储的
            buttons = item.data(6, Qt.ItemDataRole.UserRole)

            # 检查是否成功获取到按钮引用
            # 如果buttons不是None，说明这一行有按钮
            if buttons:
                # ========== 更新启动按钮的状态 ==========
                # 检查这个浏览器是否正在运行
                # is_browser_running() 返回True表示浏览器已启动，False表示未启动
                if self.browser_automation.is_browser_running(browser_id):
                    # 浏览器已启动，按钮应该显示"关闭"功能
                    buttons['launch'].setText("关闭")

                    # disconnect() 断开按钮之前绑定的所有点击事件
                    # 这很重要，避免重复绑定导致一次点击触发多个事件
                    buttons['launch'].clicked.disconnect()

                    # 重新绑定按钮点击事件到"关闭浏览器"功能
                    # lambda表达式创建匿名函数，checked是按钮点击时传递的参数（通常忽略）
                    # bid=browser_id 是闭包，捕获当前循环中的browser_id值
                    buttons['launch'].clicked.connect(lambda checked, bid=browser_id: self.close_browser(bid))
                else:
                    # 浏览器未启动，按钮应该显示"启动"功能
                    buttons['launch'].setText("启动")
                    buttons['launch'].clicked.disconnect()  # 断开旧的事件绑定
                    # 重新绑定到"启动浏览器"功能
                    buttons['launch'].clicked.connect(lambda checked, bid=browser_id: self.launch_browser(bid))

                # ========== 更新开始/暂停按钮的状态 ==========
                # 检查这个浏览器的自动化任务是否正在运行
                # is_automation_running() 返回True表示有自动化任务在执行
                if self.browser_automation.is_automation_running(browser_id):
                    # 有自动化任务在运行，需要进一步判断是"运行中"还是"已暂停"
                    # .get(browser_id) 从状态字典中获取具体的自动化状态
                    auto_status = self.browser_automation.automation_status.get(browser_id)

                    # 根据具体的自动化状态设置按钮文字
                    if auto_status == 'running':
                        # 状态是"运行中"，按钮应该显示"暂停"，让用户可以暂停任务
                        buttons['start'].setText("暂停")
                    elif auto_status == 'paused':
                        # 状态是"已暂停"，按钮应该显示"开始"，让用户可以恢复任务
                        buttons['start'].setText("开始")
                else:
                    # 没有自动化任务在运行，按钮显示"开始"，让用户可以启动新任务
                    buttons['start'].setText("开始")

    def close_browser(self, browser_id):
        """关闭指定浏览器"""
        self.browser_automation.close_browser(browser_id)
        # 更新浏览器数量显示
        self.update_browser_count_display()
        show_info_message("成功", "浏览器已关闭")

    def launch_browser(self, browser_id):
        """启动指定浏览器"""
        # 从界面获取目标网址
        target_url = self.ui.lineEdit_2.text().strip() if hasattr(self.ui, 'lineEdit_2') else ""

        # 如果没有网址，使用默认网址
        if not target_url:
            target_url = "https://www.binance.com/zh-CN/markets/alpha-initials"

        result = self.browser_automation.open_browser(browser_id, target_url)
        print("浏览器启动后的result", result)
        if result.get("success"):
            show_info_message("成功", f"浏览器启动成功并导航到:\n{target_url}")
            # 更新浏览器数量显示
            self.update_browser_count_display()
        else:
            show_error_message("错误", f"浏览器启动失败: {result.get('message', '未知错误')}")

    def toggle_automation(self, browser_id, button):
        """切换自动化状态"""
        if not self.browser_automation.is_browser_running(browser_id):
            show_error_message("错误", "请先启动浏览器")
            return

        current_text = button.text()
        if current_text == "开始":
            # 获取交易参数
            item = self.find_item_by_browser_id(browser_id)
            if item:
                buy_price = item.text(1)
                buy_quantity = item.text(2)
                run_count = item.text(5)
                min_delay = self.ui.lineEdit.text()
                max_delay = self.ui.lowlatency.text()

                # 开始自动化
                if self.browser_automation.start_automation(browser_id, buy_price, buy_quantity,
                                                          run_count, min_delay, max_delay):
                    button.setText("暂停")
        else:  # 暂停
            if self.browser_automation.pause_automation(browser_id):
                if self.browser_automation.automation_status.get(browser_id) == 'running':
                    button.setText("暂停")
                else:
                    button.setText("开始")

    def find_item_by_browser_id(self, browser_id):
        """
        根据浏览器ID查找对应的树形项（界面中的一行数据）

        这个函数的作用：
        在界面的表格中找到指定浏览器对应的那一行，以便后续更新显示内容

        参数：
            browser_id (str): 浏览器的唯一标识符，例如 "browser_123456"

        返回值：
            QTreeWidgetItem: 找到的树形项对象（代表界面中的一整行数据）
            None: 如果没找到对应的行，返回空值

        工作原理：
        1. 遍历界面表格中的每一行
        2. 检查每行存储的浏览器ID是否匹配
        3. 找到匹配的行就返回该行对象
        4. 遍历完都没找到就返回None
        """
        # topLevelItemCount() 返回表格中总共有多少行
        # range() 生成从0到行数-1的数字序列，用于遍历每一行
        for i in range(self.ui.treeWidget.topLevelItemCount()):
            # topLevelItem(i) 获取第i行的数据对象
            # item 就是界面中第i行的完整数据，包含所有列的内容
            item = self.ui.treeWidget.topLevelItem(i)

            # item.data(列索引, 数据角色) 获取存储在指定列的额外数据
            # 第0列存储了浏览器ID（之前用setData存储的）
            # Qt.ItemDataRole.UserRole 是用户自定义数据的存储角色
            # 这行代码的意思：获取第0列存储的浏览器ID，与传入的browser_id比较
            if item.data(0, Qt.ItemDataRole.UserRole) == browser_id:
                # 如果找到匹配的浏览器ID，返回这一行的完整数据对象
                # item包含了这一行的所有信息：名称、价格、数量、状态、按钮等
                return item

        # 如果遍历完所有行都没找到匹配的，返回None表示没找到
        return None

    def update_browser_status(self, browser_id, status):
        """
        更新浏览器状态显示 - 在界面上更新指定浏览器的状态文本

        这个函数的作用：
        当浏览器的状态发生变化时（比如从"未启动"变为"已启动"），
        在界面的表格中更新对应行的状态显示文本。

        参数：
            browser_id (str): 浏览器的唯一标识符，例如 "browser_123456"
            status (str): 新的状态文本，例如 "已启动"、"自动化运行中"、"已暂停" 等

        工作流程：
        1. 根据浏览器ID找到界面中对应的行
        2. 如果找到了这一行，就更新状态列的显示文本
        3. 如果没找到，说明这个浏览器不在当前界面中，不做任何操作

        调用时机：
        - 当后台线程发射status_updated信号时
        - 当浏览器启动、关闭、开始自动化、暂停等状态变化时
        """
        # 调用之前定义的查找函数，根据浏览器ID找到对应的界面行
        # item 是 QTreeWidgetItem 对象，代表界面表格中的一整行数据
        item = self.find_item_by_browser_id(browser_id)

        # 检查是否找到了对应的行
        # 如果 item 不是 None，说明找到了对应的行
        if item:
            # setText(列索引, 新文本) 设置指定列的显示文本
            # 第3列是状态列，将其文本更新为新的状态
            # 例如：item.setText(3, "自动化运行中")
            item.setText(3, status)

    def update_run_count(self, browser_id, count):
        """
        更新运行次数显示 - 在界面上更新指定浏览器的执行次数

        这个函数的作用：
        当自动化任务执行了一次交易后，在界面的表格中更新对应行的运行次数显示。

        参数：
            browser_id (str): 浏览器的唯一标识符，例如 "browser_123456"
            count (int): 新的运行次数，例如 1、2、3... 表示已经执行了多少次交易

        工作流程：
        1. 根据浏览器ID找到界面中对应的行
        2. 如果找到了这一行，就更新运行次数列的显示数字
        3. 将整数转换为字符串，因为界面显示需要文本格式

        调用时机：
        - 当后台自动化线程成功执行一次交易后
        - 通过run_count_updated信号触发此函数

        示例：
        如果自动化任务执行了3次交易，界面上就会显示"3"
        """
        # 根据浏览器ID找到界面中对应的行
        item = self.find_item_by_browser_id(browser_id)

        # 检查是否找到了对应的行
        if item:
            # setText(列索引, 文本) 设置指定列的显示文本
            # 第4列是运行次数列
            # str(count) 将整数转换为字符串，例如 str(3) 变成 "3"
            # 因为界面显示需要文本格式，不能直接显示数字
            item.setText(4, str(count))

    def launch_all_browsers(self):
        """
        启动所有浏览器 - 批量启动所有未启动的浏览器窗口

        这个函数的作用：
        一键启动所有未启动的浏览器，并导航到指定的网址。
        同时会检查浏览器数量限制，统计启动结果。

        工作流程：
        1. 从界面获取目标网址
        2. 获取所有未启动的浏览器列表
        3. 逐个启动浏览器，检查数量限制
        4. 统计启动结果
        5. 显示操作结果给用户

        特点：
        - 支持数量限制控制
        - 提供详细的操作结果反馈
        - 自动导航到指定网址
        """
        # ========== 第一步：获取目标网址 ==========
        # hasattr(对象, '属性名') 检查对象是否有指定的属性
        # 检查界面对象是否有lineEdit_2这个输入框控件
        # 如果有，就获取其文本内容；如果没有，就使用空字符串
        target_url = self.ui.lineEdit_2.text().strip() if hasattr(self.ui, 'lineEdit_2') else ""
        # .text() 获取输入框中的文本内容
        # .strip() 去除文本前后的空格，避免用户误输入空格

        # 检查是否获取到了有效的网址
        # not target_url 表示：如果target_url是空字符串，条件为True
        if not target_url:
            # 如果用户没有输入网址，使用默认的币安网址
            target_url = "https://www.binance.com/zh-CN/markets/alpha-initials"

        # ========== 第二步：获取未启动的浏览器列表 ==========
        # 调用API获取所有未启动的浏览器（page=0表示未启动）
        result = self.browser_automation.get_browser_list(page=0)

        # 检查API调用是否成功
        # .get("success") 安全地获取响应中的success字段
        # 如果success为True，说明API调用成功
        if result.get("success"):
            # 从API响应中提取浏览器列表
            # result["data"]["list"] 是浏览器信息的数组
            browsers = result["data"]["list"]

            # ========== 第三步：初始化统计变量 ==========
            success_count = 0    # 成功启动的浏览器数量
            failed_count = 0     # 启动失败的浏览器数量
            limit_reached = False # 是否达到了数量限制

            # ========== 第四步：遍历并启动每个浏览器 ==========
            # for browser in browsers 遍历浏览器列表中的每个浏览器
            # browser 是当前循环中的浏览器信息字典
            for browser in browsers:
                # 获取当前浏览器的唯一标识符
                browser_id = browser['id']

                # 检查这个浏览器是否已经在运行
                # not 表示"不"，如果浏览器没有运行，条件为True
                if not self.browser_automation.is_browser_running(browser_id):
                    # 浏览器没有运行，可以尝试启动

                    # ========== 检查数量限制 ==========
                    # 调用数量检查函数，返回两个值：
                    # can_start: 布尔值，True表示可以启动，False表示不能启动
                    # limit_message: 字符串，包含限制信息
                    can_start, limit_message = self.browser_automation.can_start_more_browsers()

                    # 如果不能启动更多浏览器（达到数量限制）
                    if not can_start:
                        # 设置标志，表示达到了限制
                        limit_reached = True
                        # break 跳出for循环，不再尝试启动其他浏览器
                        break

                    # ========== 尝试启动浏览器 ==========
                    # 调用启动浏览器的方法，传入浏览器ID和目标网址
                    res = self.browser_automation.open_browser(browser_id, target_url)

                    # 检查启动是否成功
                    # res.get("success") 获取启动结果中的success字段
                    if res.get("success"):
                        # 启动成功，成功计数器加1
                        # += 1 等价于 success_count = success_count + 1
                        success_count += 1
                    else:
                        # 启动失败，失败计数器加1
                        failed_count += 1

                    # time.sleep(1) 暂停1秒
                    # 避免启动过快，给系统一些时间处理
                    time.sleep(1)

            # ========== 第五步：更新界面显示 ==========
            # 调用方法更新浏览器数量显示（窗口标题等）
            self.update_browser_count_display()

            # ========== 第六步：构建结果消息 ==========
            # 构建要显示给用户的结果消息
            # f"成功启动 {success_count} 个浏览器" 是格式化字符串
            # {success_count} 会被替换为实际的成功数量
            message = f"成功启动 {success_count} 个浏览器"

            # 如果有启动失败的浏览器，添加失败信息
            # > 0 表示"大于0"，如果失败数量大于0
            if failed_count > 0:
                # += 表示字符串拼接，在原有消息后面添加新内容
                # \n 是换行符，让消息分行显示
                message += f"\n失败 {failed_count} 个"

            # 如果达到了数量限制，添加限制信息
            if limit_reached:
                # 获取当前的数量统计信息
                count_info = self.browser_automation.get_browser_count_info()
                # 添加限制信息到消息中
                message += f"\n已达到最大数量限制 ({count_info['status_text']})"

            # ========== 第七步：显示结果给用户 ==========
            # show_info_message() 显示信息对话框
            # 第一个参数是对话框标题，第二个参数是消息内容
            show_info_message("完成", message)

    def close_all_browsers(self):
        """关闭所有浏览器"""
        closed_count = 0
        for browser_id in list(self.browser_automation.running_browsers.keys()):
            self.browser_automation.close_browser(browser_id)
            closed_count += 1

        # 更新浏览器数量显示
        self.update_browser_count_display()
        show_info_message("完成", f"成功关闭 {closed_count} 个浏览器")

    def start_all_automation(self):
        """开始所有自动化"""
        started_count = 0
        min_delay = self.ui.lineEdit.text()
        max_delay = self.ui.lowlatency.text()

        # 验证延迟设置
        is_valid, error_msg = self.browser_automation.validate_delay_settings(min_delay, max_delay)
        if not is_valid:
            show_error_message("延迟设置错误", error_msg)
            return

        for i in range(self.ui.treeWidget.topLevelItemCount()):
            item = self.ui.treeWidget.topLevelItem(i)
            browser_id = item.data(0, Qt.ItemDataRole.UserRole)

            if (self.browser_automation.is_browser_running(browser_id) and
                not self.browser_automation.is_automation_running(browser_id)):

                buy_price = item.text(1)
                buy_quantity = item.text(2)
                run_count = item.text(5)

                if self.browser_automation.start_automation(browser_id, buy_price, buy_quantity,
                                                          run_count, min_delay, max_delay):
                    started_count += 1
                    # 更新按钮状态
                    buttons = item.data(6, Qt.ItemDataRole.UserRole)
                    if buttons:
                        buttons['start'].setText("暂停")

        show_info_message("完成", f"成功开始 {started_count} 个自动化任务")

    def stop_all_automation(self):
        """停止所有自动化"""
        stopped_count = 0
        for i in range(self.ui.treeWidget.topLevelItemCount()):
            item = self.ui.treeWidget.topLevelItem(i)
            browser_id = item.data(0, Qt.ItemDataRole.UserRole)

            if self.browser_automation.is_automation_running(browser_id):
                self.browser_automation.stop_automation(browser_id)
                stopped_count += 1
                # 更新按钮状态
                buttons = item.data(6, Qt.ItemDataRole.UserRole)
                if buttons:
                    buttons['start'].setText("开始")

        show_info_message("完成", f"成功停止 {stopped_count} 个自动化任务")

    def launch_selected_browser(self):
        """启动选中的浏览器"""
        current_item = self.ui.treeWidget.currentItem()
        if current_item:
            browser_id = current_item.data(0, Qt.ItemDataRole.UserRole)
            self.launch_browser(browser_id)
        else:
            show_error_message("错误", "请先选择一个浏览器")

    def start_selected_automation(self):
        """开始选中的自动化"""
        current_item = self.ui.treeWidget.currentItem()
        if current_item:
            browser_id = current_item.data(0, Qt.ItemDataRole.UserRole)
            buttons = current_item.data(6, Qt.ItemDataRole.UserRole)
            if buttons:
                self.toggle_automation(browser_id, buttons['start'])
        else:
            show_error_message("错误", "请先选择一个浏览器")

    def pause_selected_automation(self):
        """暂停选中的自动化"""
        current_item = self.ui.treeWidget.currentItem()
        if current_item:
            browser_id = current_item.data(0, Qt.ItemDataRole.UserRole)
            if self.browser_automation.is_automation_running(browser_id):
                self.browser_automation.pause_automation(browser_id)
                buttons = current_item.data(6, Qt.ItemDataRole.UserRole)
                if buttons:
                    if self.browser_automation.automation_status.get(browser_id) == 'running':
                        buttons['start'].setText("暂停")
                    else:
                        buttons['start'].setText("开始")
        else:
            show_error_message("错误", "请先选择一个浏览器")

    def stop_selected_automation(self):
        """停止选中的自动化"""
        current_item = self.ui.treeWidget.currentItem()
        if current_item:
            browser_id = current_item.data(0, Qt.ItemDataRole.UserRole)
            self.browser_automation.stop_automation(browser_id)
            buttons = current_item.data(6, Qt.ItemDataRole.UserRole)
            if buttons:
                buttons['start'].setText("开始")
        else:
            show_error_message("错误", "请先选择一个浏览器")

    def on_item_double_clicked(self, item, column):
        """处理双击编辑事件"""
        # 只允许编辑买入价格(1)、买入数量(2)、设置运行次数(5)列

        if column in [1, 2, 5]:
            print("你双击了:", item.text(column))
            #self.ui.treeWidget.editItem(item, column)
            #item.setFlags(item.flags() | Qt.ItemFlag.ItemIsEditable)



if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec())
